'use client';

import { Property } from '@/types/property';
import { ReactNode, createContext, useContext, useState } from 'react';

type Context = {
  selectedIds: number[];
  setSelectedIds: (ids: number[]) => void;
};

export const ListingsContext = createContext<Context>({} as Context);

type ContextProps = {
  children: ReactNode;
};

export const ListingsContextContainer = ({ children }: ContextProps) => {
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  return (
    <ListingsContext.Provider value={{ selectedIds, setSelectedIds }}>
      {children}
    </ListingsContext.Provider>
  );
};

export const useListings = () => {
  const context = useContext(ListingsContext);
  if (!context) {
    throw new Error(
      'useListings must be used within a ListingsContextProvider'
    );
  }
  return context;
};
