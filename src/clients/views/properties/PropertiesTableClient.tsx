'use client';
import React, { useState, ReactNode, useCallback, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Listing } from '@/types/property';
import {
  SortConfig,
  Column,
  PropertiesTableBaseProps,
  DEFAULT_PROPS,
} from './types';
import { twMerge } from 'tailwind-merge';
import PropertyTableItem from './PropertyTableItem';
import { useUpdateSearchParam } from '@/hooks/useUpdateSearchParam';
import LoadingSpinner from '@/clients/ui/loading-spinner';

export type PropertiesTableClientProps = PropertiesTableBaseProps & {
  staticTable: ReactNode;
  columns: Column[];
};

/**
 * Client component for Properties Table
 * Handles all interactive functionality: selection, sorting, pagination
 */
export const PropertiesTableClient = ({
  data = DEFAULT_PROPS.data,
  total = DEFAULT_PROPS.total,
  flag: _flag = DEFAULT_PROPS.flag,
  offset = DEFAULT_PROPS.offset,
  currentPage = DEFAULT_PROPS.currentPage,
  searchInfor: _searchInfor = DEFAULT_PROPS.searchInfor,
  limit = DEFAULT_PROPS.limit,
  staticTable,
  columns,
  onPagesChanged,
  onSort,
  onSubset,
  onShowAllPage,
  onLoad,
}: PropertiesTableClientProps) => {
  const updateParam = useUpdateSearchParam();
  const searchParams = useSearchParams();
  // State
  const [selectedItems, setSelectedItems] = useState<Listing[]>([]);
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: null,
    direction: 'asc',
  });

  // UI state
  const [isUpdatingSearch, setIsUpdatingSearch] = useState(false);

  // Event handlers
  const handleSelectAll = () => {
    if (selectedItems.length === data.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(data);
    }
  };

  const handleSelectItem = (property: Listing) => {
    const isSelected = selectedItems.find(
      (item) => item.listing_id === property.listing_id
    );

    if (isSelected) {
      const newSelected = selectedItems.filter(
        (item) => item.listing_id !== property.listing_id
      );
      setSelectedItems(newSelected);
    } else {
      const newSelected = [...selectedItems, property];
      setSelectedItems(newSelected);
    }
  };

  const handleSort = useCallback(
    (key: string) => {
      let direction: 'asc' | 'desc' = 'asc';
      if (sortConfig.key === key && sortConfig.direction === 'asc') {
        direction = 'desc';
      }
      setSortConfig({ key, direction });
      setIsUpdatingSearch(true);
      updateParam(
        'ordering',
        `not_renting_year,${direction === 'asc' ? key : `-${key}`}`
      );
    },
    [sortConfig, updateParam]
  );

  // const handlePageChange = (page: number) => {
  //   if (page >= 1 && page <= Math.ceil(total / limit)) {
  //     onPagesChanged?.(page);
  //   }
  // };

  // const handleRowClick = (property: Property) => {
  //   router.push(`/rental-listings/${property.listing_id}/overview`);
  // };

  const handleSubset = () => {
    onSubset?.(selectedItems);
    setSelectedItems([]);
  };

  const handleLoad = () => {
    onLoad?.();
  };

  // Reset loading state when search parameters change
  useEffect(() => {
    setIsUpdatingSearch(false);
  }, [searchParams]);

  return (
    <div className="bg-white shadow-sm rounded-lg overflow-hidden">
      <div className="overflow-x-auto mt-[30px]">
        <table className="min-w-full divide-y divide-gray-200 table-fixed">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={twMerge(
                    `px-2.5 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${column.className}`
                  )}
                >
                  {column.key === 'checkbox' ? (
                    <input
                      type="checkbox"
                      checked={
                        selectedItems.length === data.length && data.length > 0
                      }
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  ) : (
                    <button
                      onClick={() => column.sortable && handleSort(column.key)}
                      className={`flex items-center space-x-1 uppercase font-medium ${
                        column.sortable
                          ? 'hover:text-gray-700 cursor-pointer'
                          : ''
                      }`}
                      disabled={!column.sortable}
                    >
                      <span>{column.label}</span>
                      {column.sortable && sortConfig.key === column.key && (
                        <span className="text-blue-600">
                          {sortConfig.direction === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </button>
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((property) => (
              <PropertyTableItem
                key={property.listing_id}
                property={property}
              />
            ))}
          </tbody>
        </table>
      </div>

      {isUpdatingSearch && (
        <div className="absolute inset-0 bg-white/70 flex items-center justify-center z-[9999] rounded">
          <LoadingSpinner className="w-10 h-10 text-olive" />
        </div>
      )}
    </div>
  );
};
