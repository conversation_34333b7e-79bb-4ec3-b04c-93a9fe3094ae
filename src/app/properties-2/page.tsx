'use server';
import { Listing } from '@/types/property';
import { getAllListings } from '@/app/actions/property';
import { getSearchParams } from '@/utils/common';
import Image from 'next/image';
import PropertiesFilter from '@/clients/views/properties/PropertiesFilter';
import PropertiesTable from '@/clients/views/properties/PropertiesTable';
import StickyFooter from '@/clients/views/properties/StickyFooter';
import { ListingsContextContainer } from '@/clients/contexts/ListingsContext';

export default async function Properties({
  searchParams,
}: {
  searchParams: { [key: string]: string };
}) {
  const defaultParams = {
    show_rates: 'true',
    ordering: 'not_renting_year,priority,-calendar_updated_at',
  };

  const params = getSearchParams({
    ...searchParams,
    ...defaultParams,
  });

  const data = await getAllListings<{
    results: Listing[];
    count: number;
  }>(params);

  return (
    <ListingsContextContainer>
      <main className="min-h-screen py-4 px-5 md:py-5 md:px-10">
        <div className="flex items-center space-x-3 mb-8">
          <Image
            alt="listings icon"
            src="/images/icons/rental-list.svg"
            width={30}
            height={30}
          />
          <p className="text-xl font-medium text-gray-900">Rental Listings</p>
        </div>
        <PropertiesFilter />

        <PropertiesTable data={data?.results ?? []} total={data?.count ?? 0} />
        <StickyFooter />
      </main>
    </ListingsContextContainer>
  );
}
